"use client"
import { useEffect, useState } from "react"

export function useIsMobile(query = "(max-width: 768px)"): boolean {
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    // Ensure window is defined (for SSR compatibility, though this is client-side)
    if (typeof window === "undefined") {
      return
    }

    const mediaQuery = window.matchMedia(query)
    const handleChange = () => setIsMobile(mediaQuery.matches)

    handleChange() // Initial check
    mediaQuery.addEventListener("change", handleChange)

    return () => mediaQuery.removeEventListener("change", handleChange)
  }, [query])

  return isMobile
}
