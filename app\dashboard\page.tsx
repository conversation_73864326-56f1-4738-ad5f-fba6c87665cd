"use client"

import { StatCard } from "@/components/stat-card"
import { mockUsers, mockDrivers, mockRides, mockPayments } from "@/lib/mock-data"
import {
  DollarSign,
  Users,
  Car,
  RouteIcon,
  Activity,
  BarChart3,
  MapPinned,
  ShieldCheck,
  Alert<PERSON>riangle,
} from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useAuth } from "@/context/auth-context"
import { useMemo } from "react"
import Link from "next/link"

export default function DashboardPage() {
  const { user } = useAuth()
  const role = user?.role || "regional_operator"
  const region = user?.region

  const filteredPayments = useMemo(
    () => (region && role !== "admin" ? mockPayments.filter((p) => p.region === region) : mockPayments),
    [region, role],
  )
  const filteredUsers = useMemo(
    () => (region && role !== "admin" ? mockUsers.filter((u) => u.region === region) : mockUsers),
    [region, role],
  )
  const filteredDrivers = useMemo(
    () => (region && role !== "admin" ? mockDrivers.filter((d) => d.region === region) : mockDrivers),
    [region, role],
  )
  const filteredRides = useMemo(
    () => (region && role !== "admin" ? mockRides.filter((r) => r.region === region) : mockRides),
    [region, role],
  )

  const totalRevenue = filteredPayments.filter((p) => p.status === "completed").reduce((sum, p) => sum + p.amount, 0)
  const activeRidesCount = filteredRides.filter(
    (r) =>
      r.status === "in_progress" ||
      r.status === "accepted" ||
      r.status === "en_route_pickup" ||
      r.status === "arrived_pickup",
  ).length

  const stats = []

  if (role === "admin" || role === "regional_head") {
    stats.push({
      title: role === "admin" ? "Total Revenue" : "Regional Revenue",
      value: `रू ${totalRevenue.toLocaleString()}`,
      icon: DollarSign,
      change: "+5.2%",
      changeType: "positive" as const,
      description: `Revenue from completed rides ${role === "admin" ? "system-wide" : `in ${region}`}.`,
    })
  }

  stats.push(
    {
      title: role === "admin" ? "Active Users" : "Regional Users",
      value: filteredUsers.filter((u) => u.status === "active").length.toString(),
      icon: Users,
      change: "+10.1%",
      changeType: "positive" as const,
      description: `Verified users ${role === "admin" ? "system-wide" : `in ${region}`}.`,
    },
    {
      title: role === "admin" ? "Online Drivers" : "Regional Online Drivers",
      value: filteredDrivers.filter((d) => d.status === "active_online").length.toString(),
      icon: Car,
      change: "-1.5%",
      changeType: "negative" as const,
      description: `Drivers online ${role === "admin" ? "system-wide" : `in ${region}`}.`,
    },
    {
      title: role === "admin" ? "Ongoing Rides" : "Regional Ongoing Rides",
      value: activeRidesCount.toString(),
      icon: RouteIcon,
      change: "+3 since last hour",
      changeType: "positive" as const,
      description: `Rides in progress ${role === "admin" ? "system-wide" : `in ${region || "assigned area"}`}.`,
    },
  )

  const dailyRideCount = filteredRides.filter(
    (r) => new Date(r.requestTime).toDateString() === new Date().toDateString(),
  ).length
  if (role === "regional_operator") {
    stats.length = 0 // Clear previous stats for operator
    stats.push(
      {
        title: "Today's Rides (Area)",
        value: dailyRideCount.toString(),
        icon: RouteIcon,
        description: `Total rides in your assigned area today.`,
      },
      {
        title: "Available Drivers (Area)",
        value: filteredDrivers.filter((d) => d.status === "active_online").length.toString(),
        icon: Car,
        description: `Drivers currently online in your area.`,
      },
    )
  }

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <StatCard key={stat.title} {...stat} />
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPinned className="h-5 w-5 text-primary" />
              Real-time Ride Monitoring
            </CardTitle>
            <CardDescription>
              {role === "admin"
                ? "System-wide view of all active rides."
                : `Live map of ongoing rides in ${region || "your assigned area"}.`}{" "}
              (Placeholder)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[350px] flex items-center justify-center bg-muted/30 rounded-lg border border-dashed">
              <p className="text-muted-foreground text-center">
                Map Integration Placeholder
                <br />
                <span className="text-xs">(e.g., Leaflet or Google Maps)</span>
              </p>
            </div>
          </CardContent>
        </Card>

        {(role === "admin" || role === "regional_head") && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ShieldCheck className="h-5 w-5 text-primary" />
                System Health
              </CardTitle>
              <CardDescription>Overview of system status and performance. (Placeholder)</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                <span className="text-sm font-medium">API Status</span>
                <span className="text-sm text-status-green font-semibold">Operational</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                <span className="text-sm font-medium">Database Connectivity</span>
                <span className="text-sm text-status-green font-semibold">Connected</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                <span className="text-sm font-medium">Payment Gateway</span>
                <span className="text-sm text-status-yellow font-semibold">Degraded</span>
              </div>
              <Button
                variant="outline"
                className="w-full mt-2 bg-background text-foreground hover:bg-secondary"
                asChild
              >
                <Link href="/dashboard/system-status">View Detailed Status</Link>
              </Button>
            </CardContent>
          </Card>
        )}
        {role === "regional_operator" && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-status-orange" />
                Operational Alerts
              </CardTitle>
              <CardDescription>Key alerts for your operational area.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="p-3 bg-muted/30 rounded-lg">
                <p className="text-sm font-medium">High Demand Zone: Thamel</p>
                <p className="text-xs text-muted-foreground">Consider reallocating drivers.</p>
              </div>
              <div className="p-3 bg-muted/30 rounded-lg">
                <p className="text-sm font-medium">Driver Offline: DRV-007</p>
                <p className="text-xs text-muted-foreground">Check driver status.</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {(role === "admin" || role === "regional_head") && (
        <div className="grid grid-cols-1 lg:grid-cols-7 gap-6">
          <Card className="lg:col-span-4">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-primary" />
                {role === "admin" ? "System Revenue Overview" : "Regional Revenue Overview"}
              </CardTitle>
              <CardDescription>Monthly revenue trend and key financial insights. (Placeholder)</CardDescription>
            </CardHeader>
            <CardContent className="pl-2">
              <div className="h-[350px] flex items-center justify-center bg-muted/30 rounded-lg border border-dashed">
                <p className="text-muted-foreground text-center">
                  Revenue Chart Placeholder
                  <br />
                  <span className="text-xs">(e.g., Chart.js Integration)</span>
                </p>
              </div>
            </CardContent>
          </Card>
          <Card className="lg:col-span-3">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5 text-primary" />
                Recent Activity {role !== "admin" && `(${region || "Area"})`}
              </CardTitle>
              <CardDescription>Latest rides, registrations, and important system events.</CardDescription>
            </CardHeader>
            <CardContent className="h-[390px] overflow-y-auto space-y-4 pr-2">
              {filteredRides.slice(0, 3).map((ride) => (
                <div key={ride.id} className="flex items-start p-3 rounded-lg hover:bg-muted/30 transition-colors">
                  <RouteIcon className="h-5 w-5 text-blue-500 mr-3 mt-1 shrink-0" />
                  <div>
                    <p className="text-sm font-medium leading-tight">
                      New Ride: {ride.pickupLocation} to {ride.dropoffLocation}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      User: {ride.userName || "N/A"} • Fare: रू {ride.fare} • Status:{" "}
                      <span className="font-medium capitalize">{ride.status.replace(/_/g, " ")}</span>
                    </p>
                  </div>
                </div>
              ))}
              {filteredUsers.slice(0, 2).map((user) => (
                <div key={user.id} className="flex items-start p-3 rounded-lg hover:bg-muted/30 transition-colors">
                  <Users className="h-5 w-5 text-status-green mr-3 mt-1 shrink-0" />
                  <div>
                    <p className="text-sm font-medium leading-tight">New User: {user.name}</p>
                    <p className="text-xs text-muted-foreground">Email: {user.email}</p>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
