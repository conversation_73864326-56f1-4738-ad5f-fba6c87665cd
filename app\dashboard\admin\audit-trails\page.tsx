"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import Link from "next/link"
import { useAuth } from "@/context/auth-context"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { History, ShieldAlert } from "lucide-react"

export default function AuditTrailsPage() {
  const { user } = useAuth()

  if (user?.role !== "admin") {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <Alert variant="destructive" className="max-w-md">
          <ShieldAlert className="h-4 w-4" />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>You do not have permission to view Audit Trails.</AlertDescription>
        </Alert>
        <Button asChild variant="link" className="mt-4">
          <Link href="/dashboard">Go to Dashboard</Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard">Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Audit Trails</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold flex items-center">
          <History className="mr-3 h-8 w-8 text-primary" />
          Audit Trails
        </h1>
      </div>
      <CardDescription>
        Review logs of significant actions performed within the system by administrators and users.
      </CardDescription>

      <Card>
        <CardHeader>
          <CardTitle>Audit Log (Placeholder)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] flex items-center justify-center bg-muted/30 rounded-lg border border-dashed">
            <p className="text-muted-foreground text-center">
              Audit trail data will be displayed here.
              <br />
              (e.g., User role changes, settings modifications, sensitive data access)
            </p>
          </div>
          <p className="text-sm text-muted-foreground mt-4">
            This page is a placeholder. In a real application, it would display a filterable and searchable log of
            important system events and user actions for security and compliance purposes.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
