"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  <PERSON>readcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import Link from "next/link"
import { useAuth } from "@/context/auth-context"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { BarChartBig, ShieldAlert } from "lucide-react"

export default function RegionalPerformanceAnalysisPage() {
  const { user } = useAuth()

  if (user?.role !== "regional_head") {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <Alert variant="destructive" className="max-w-md">
          <ShieldAlert className="h-4 w-4" />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>You do not have permission to view Regional Performance Analysis.</AlertDescription>
        </Alert>
        <Button asChild variant="link" className="mt-4">
          <Link href="/dashboard">Go to Dashboard</Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard">Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Regional Performance Analysis</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold flex items-center">
          <BarChartBig className="mr-3 h-8 w-8 text-primary" />
          Performance Analysis ({user?.region || "Your Region"})
        </h1>
      </div>
      <CardDescription>Analyze key performance indicators (KPIs) for your operational region.</CardDescription>

      <Card>
        <CardHeader>
          <CardTitle>Regional KPIs (Placeholder)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] flex items-center justify-center bg-muted/30 rounded-lg border border-dashed">
            <p className="text-muted-foreground text-center">
              Detailed performance charts and data for {user?.region} will be displayed here.
              <br />
              (e.g., Ride completion rates, driver utilization, customer satisfaction scores)
            </p>
          </div>
          <p className="text-sm text-muted-foreground mt-4">
            This page is a placeholder. A full implementation would include interactive charts, data tables, and
            filtering options for in-depth regional performance review.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
