"use client"

import type React from "react"

import { useState } from "react"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useAuth } from "@/context/auth-context"
// No need for useRouter here if login handles redirection

export default function LoginPage() {
  const [email, setEmail] = useState("<EMAIL>") // Default for demo
  const [password, setPassword] = useState("password_admin") // Default for demo
  const [isLoading, setIsLoading] = useState(false)
  const { login } = useAuth()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    // In a real application, this form submission would occur over HTTPS.
    // The backend would receive the email and password,
    // hash the password using a strong algorithm (e.g., bcrypt),
    // and compare it against the stored hash for the user.
    console.log("Attempting login with:", { email, password })
    await login(email, password)
    // login function now handles redirection and toast messages
    setIsLoading(false)
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-muted/40">
      <Card className="w-full max-w-sm">
        <CardHeader className="text-center">
          <Image src="/sarathi-logo.svg" alt="Sarathi Logo" width={80} height={80} className="mx-auto mb-4" />
          <CardTitle className="text-2xl">Sarathi Management Center Login</CardTitle>
          <CardDescription>Enter your credentials to access the Management Center.</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={isLoading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={isLoading}
              />
            </div>
            <Button
              type="submit"
              className="w-full bg-primary hover:bg-primary/90 text-primary-foreground"
              disabled={isLoading}
            >
              {isLoading ? "Logging in..." : "Login"}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
