# Sarathi Management Center - Implementation Context, RBAC, and Security (Iteration 3)

## 1. Overview

This document outlines the implementation details for the "Sarathi Management Center," focusing on Role-Based Access Control (RBAC), a secure login approach, and overall security considerations. It builds upon previous iterations that established user roles and their differentiated access to application features.

**Key Aspects Covered:**
- User Roles and Privileges
- Secure Login Mechanism (Design and Best Practices)
- Mitigation of Common Cyberattacks
- API Integration Points (Conceptual)
- Styling and Theming
- User Experience (RBAC Impact)

## 2. Technology Stack

- **Frontend**: Next.js (App Router), React, TypeScript, Tailwind CSS
- **UI Components**: shadcn/ui
- **State Management**: React Context API (for Auth and Sidebar state)
- **Mock Data**: Used for demonstration purposes in place of a live backend.

## 3. User Roles and Privileges

The application defines three primary administrative roles, each with distinct capabilities:

1.  **ADMIN**:
*   Full access to all system functionalities.
*   Manages system-wide settings (fare rules, global geo-zones, global promotions, app versions).
*   Oversees all users, drivers, rides, payments, support tickets, and financial reports across all regions.
*   Can perform all administrative actions (add/edit/delete users/drivers, approve/reject applications, issue refunds, resolve high-level disputes).
*   Access to Admin Tools.

2.  **REGIONAL HEAD**:
*   Manages operations within a specific assigned region.
*   Views and manages users, drivers, rides, payments, support tickets, and financial reports pertaining to their region.
*   Can add new users and drivers for their region.
*   Can approve/reject driver applications for their region.
*   Can manage regional settings (e.g., regional geo-zones, regional promotions with defined limits like $200 per campaign).
*   Can handle ride disputes and fare adjustments within certain monetary limits (e.g., up to $100). Actions exceeding these limits require Admin approval.
*   Can escalate support tickets to Admin.

3.  **REGIONAL OPERATOR**:
*   Handles day-to-day operational tasks within an assigned region.
*   Primarily views data relevant to their region (dashboard stats, users, drivers, rides, support tickets).
*   Limited action capabilities:
    *   Can view user and driver details for support purposes.
    *   Can report incidents related to rides.
    *   Can issue basic refunds for rides within a very strict limit (e.g., up to $25).
    *   Can escalate support tickets to their Regional Head.
*   No access to financial transaction tables, system-wide settings, or financial report generation.

Privileges are enforced in the UI by:
- Conditionally rendering navigation items in the sidebar.
- Conditionally rendering action buttons or entire sections on pages.
- Filtering data displayed in tables based on the user's role and assigned region.
- Displaying "Access Denied" messages or alerts for restricted features.
- Simulating monetary/action limits with toast notifications.

The `AuthContext` (`context/auth-context.tsx`) manages the current user's session and role, which is then used throughout the application to determine access levels.

## 4. Secure Login Mechanism

A secure login process is paramount. The following outlines the recommended approach:

1.  **Client-Side**:
*   The login form (`app/login/page.tsx`) collects the user's email and password.
*   **Crucially, the form submission MUST occur over an HTTPS connection in a production environment.** HTTPS encrypts the data in transit, protecting it from eavesdropping and man-in-the-middle attacks. Client-side encryption of credentials before sending over HTTPS is generally not recommended as it can add unnecessary complexity and may not significantly enhance security if not implemented flawlessly (e.g., key management issues).

2.  **Server-Side (Conceptual - as no backend is built in this project)**:
*   The backend API endpoint receives the login credentials (email and password) over HTTPS.
*   **Password Hashing**: The server **NEVER** stores plaintext passwords. Upon receiving the password, it should be hashed using a strong, salted, and adaptive hashing algorithm like **bcrypt** or **Argon2**.
*   **Credential Validation**: The server retrieves the stored hashed password for the provided email. It then compares the hash of the submitted password with the stored hash.
*   **Session Management**: If the credentials are valid, the server generates a secure session token (e.g., a JWT - JSON Web Token, or a secure session cookie). This token is sent back to the client.
*   The client stores this token (e.g., in an HttpOnly, Secure cookie or localStorage - though cookies are generally preferred for web session tokens for better CSRF protection) and includes it in subsequent requests to authenticated endpoints.

3.  **Token Handling in Frontend**:
*   The `AuthContext` (`login` function) simulates receiving and storing this token (currently in localStorage for demo purposes).
*   For subsequent API calls, this token would be included in the `Authorization` header (e.g., `Bearer <token>`).

## 5. Security Considerations & Attack Mitigation

Beyond the login mechanism, several security aspects are important:

*   **HTTPS Everywhere**: All communication with the backend, not just login, must be over HTTPS.
*   **Password Policies**: Enforce strong password policies (length, complexity, uniqueness) during user registration/password changes (server-side).
*   **Input Validation**:
*   **Client-Side**: Basic validation for better UX.
*   **Server-Side**: Rigorous validation of all incoming data to prevent malformed requests and potential attacks. This is the most critical validation.
*   **SQL Injection Prevention (Server-Side)**:
*   Use parameterized queries (prepared statements) or a reputable ORM (Object-Relational Mapper) that handles this automatically.
*   Never construct SQL queries by directly concatenating user input.
*   **Cross-Site Scripting (XSS) Prevention**:
*   Sanitize user-generated content before rendering it in the HTML.
*   Use modern frontend frameworks like React, which often provide some built-in XSS protection by default when rendering data (e.g., by escaping content).
*   Set appropriate `Content-Security-Policy` (CSP) headers.
*   **Cross-Site Request Forgery (CSRF) Prevention (Server-Side & Client-Side)**:
*   Use anti-CSRF tokens for any state-changing requests (e.g., forms submitting POST, PUT, DELETE requests).
*   Use `SameSite` attributes on cookies.
*   **Privilege Escalation Prevention**:
*   **Server-Side Authorization**: Critically, all authorization checks (what a user is allowed to do or see) MUST be performed on the server-side for every request. Client-side UI restrictions are for UX only and can be bypassed.
*   **Principle of Least Privilege**: Users should only have the permissions necessary to perform their tasks.
*   **Secure Session Management**: Ensure session tokens are securely generated, transmitted, stored, and invalidated (e.g., on logout, timeout).
*   **Rate Limiting & Account Lockout**: Implement rate limiting on login attempts and other sensitive endpoints to prevent brute-force attacks. Implement account lockout policies after multiple failed login attempts.
*   **Regular Security Audits & Dependency Updates**: Keep all libraries and dependencies up-to-date to patch known vulnerabilities. Conduct regular security audits.
*   **Error Handling**: Avoid leaking sensitive information in error messages. Provide generic error messages to the client while logging detailed errors on the server.

## 6. API Integration Points (Conceptual - RBAC Context)

(This section remains largely the same as Iteration 2, emphasizing that all backend endpoints must enforce RBAC checks based on the authenticated user's session/token.)

- **Authentication**:
- `POST /auth/admin/login`: (Email, Password) -> Returns JWT/Session Token
- `POST /auth/admin/logout`: Invalidates session/token
- **User Management**:
- `GET /admin/users?region=[region_if_applicable]`: (Admin, Regional Head, Regional Operator - filtered)
- `POST /admin/users`: (Admin, Regional Head - for their region)
- `PUT /admin/users/:id`: (Admin, Regional Head - for users in their region)
- `DELETE /admin/users/:id`: (Admin only)
- `PUT /admin/users/:id/status`: (Admin, Regional Head - for status changes like approve/suspend)
- **Driver Management**:
- `GET /admin/drivers?region=[region_if_applicable]`: (Admin, Regional Head, Regional Operator - filtered)
- `POST /admin/drivers`: (Admin, Regional Head - for their region)
- `PUT /admin/drivers/:id`: (Admin, Regional Head - for drivers in their region)
- `PUT /admin/drivers/:id/status`: (Admin, Regional Head - for approve/reject)
- **Ride Management**:
- `GET /admin/rides?region=[region_if_applicable]`: (Admin, Regional Head, Regional Operator - filtered)
- `PUT /admin/rides/:id/cancel`: (Admin, Regional Head)
- `POST /admin/rides/:id/dispute`: (Admin, Regional Head - backend checks $100 limit for RH)
- `PUT /admin/rides/:id/fare`: (Admin, Regional Head - backend checks $100 limit for RH)
- `POST /admin/rides/:id/refund`: (Regional Operator - backend checks $25 limit)
- **Payment Management**:
- `GET /admin/payments?region=[region_if_applicable]`: (Admin, Regional Head - filtered)
- `POST /admin/payments/:id/refund`: (Admin)
- **System Settings**:
- `GET /admin/settings`: (Admin, Regional Head - filtered settings)
- `PUT /admin/settings/fares`: (Admin)
- `PUT /admin/settings/zones`: (Admin - global; Regional Head - regional, backend checks scope)
- `POST /admin/settings/promotions`: (Admin - global; Regional Head - regional, backend checks $200 limit for RH)
- **Support Tickets**:
- `GET /admin/support-tickets?region=[region_if_applicable]`: (Admin, Regional Head, Regional Operator - filtered)
- `POST /admin/support-tickets/:id/reply`: (All roles with access to the ticket)
- `PUT /admin/support-tickets/:id/status`: (Admin, Regional Head - for resolve/assign)
- `POST /admin/support-tickets/:id/escalate`: (All roles, backend routes appropriately)
- **Financial Reports**:
- `GET /admin/reports/financial?type=[report_type]&region=[region_if_applicable]`: (Admin, Regional Head - backend filters by type and region)

## 7. Styling and Theming

- Uses Tailwind CSS for utility-first styling.
- `globals.css` defines base styles and CSS variables for theming (light/dark modes, sidebar).
- `tailwind.config.ts` includes custom color palettes (Sarathi brand colors) and shadcn/ui presets.
- Sidebar has its own set of CSS variables for distinct theming if desired (e.g., `var(--sidebar-background)`).

## 8. User Experience (RBAC & Security Impact)

- **Clarity of Role**: Users see an interface tailored to their responsibilities, reducing cognitive load.
- **Security Awareness (Conceptual)**: While not visually explicit to the end-user, the underlying secure login and session management contribute to a trustworthy experience.
- **Actionable Feedback**: Toasts for simulated limits or access restrictions guide the user.
- **Progressive Disclosure**: Complex features are hidden from users who don't need them, simplifying their workflow.

## 9. Mockup Pages & Navigation

Mockup pages have been created for:
- `Account Settings`
- `Help Center`
- `System Status`
- Detail views for `Users`, `Drivers`, `Rides`, and `Support Tickets`.
These pages are linked from relevant buttons/actions within the application to provide a more complete navigational flow and demonstrate intended content areas.

## 10. Future Considerations / Full Implementation Notes

- **Full Backend Implementation**: Essential for actual data persistence, business logic, and robust security enforcement.
- **Real-time Features**: For live ride tracking or notifications, WebSockets (e.g., Socket.IO) would be integrated.
- **Comprehensive Testing**: Unit, integration, and end-to-end tests, including security penetration testing.
- **Detailed API Documentation**: Using tools like Swagger/OpenAPI for backend APIs.
- **Advanced User Management**: Features like password reset flows, email verification, audit logs for admin actions.
