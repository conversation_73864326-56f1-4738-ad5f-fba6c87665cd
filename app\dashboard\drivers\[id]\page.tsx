"use client"

import { Label } from "@/components/ui/label"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { mockDrivers } from "@/lib/mock-data"
import { useParams, useRouter } from "next/navigation"
import Link from "next/link"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { ArrowLeft, Edit, FileText, TrendingUp, Star } from "lucide-react"
import { cn } from "@/lib/utils"

const getDriverStatusBadgeClass = (status: string | undefined) => {
  switch (status) {
    case "approved":
    case "active_online":
    case "active_offline":
      return "bg-status-green text-status-green-foreground"
    case "pending":
      return "bg-status-yellow text-status-yellow-foreground"
    case "rejected":
      return "bg-status-red text-status-red-foreground"
    default:
      return "bg-status-gray text-status-gray-foreground"
  }
}

export default function DriverDetailPage() {
  const params = useParams()
  const router = useRouter()
  const driverId = params.id as string

  const driver = mockDrivers.find((d) => d.id === driverId)

  if (!driver) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <p className="text-xl text-muted-foreground">Driver not found.</p>
        <Button onClick={() => router.back()} variant="outline" className="mt-4">
          <ArrowLeft className="mr-2 h-4 w-4" /> Go Back
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard">Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard/drivers">Driver Management</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{driver.name}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold">Driver Details</h1>
        <Button onClick={() => router.back()} variant="outline">
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Drivers
        </Button>
      </div>

      <Card>
        <CardHeader className="flex flex-row items-start gap-4 space-y-0">
          <Avatar className="h-20 w-20">
            <AvatarImage
              src={driver.profilePictureUrl || "/placeholder.svg?width=80&height=80&text=Driver"}
              alt={driver.name}
            />
            <AvatarFallback>{driver.name.substring(0, 2).toUpperCase()}</AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <CardTitle className="text-2xl">{driver.name}</CardTitle>
            <CardDescription>ID: {driver.id}</CardDescription>
            <div className="flex items-center gap-2 mt-2">
              <Badge className={cn("capitalize", getDriverStatusBadgeClass(driver.status))}>
                {driver.status.replace("_", " ")}
              </Badge>
              {driver.rating > 0 && (
                <div className="flex items-center text-sm text-muted-foreground">
                  <Star className="h-4 w-4 text-yellow-400 mr-1 fill-yellow-400" />
                  {driver.rating.toFixed(1)}
                </div>
              )}
            </div>
          </div>
          <div>
            <Button variant="outline" size="sm" asChild>
              <Link href={`/dashboard/drivers/${driver.id}/edit`}>
                <Edit className="mr-2 h-4 w-4" /> Edit
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm text-muted-foreground">Email</Label>
              <p className="font-medium">{driver.email}</p>
            </div>
            <div>
              <Label className="text-sm text-muted-foreground">Phone</Label>
              <p className="font-medium">{driver.phone}</p>
            </div>
            <div>
              <Label className="text-sm text-muted-foreground">License Number</Label>
              <p className="font-medium">{driver.licenseNumber}</p>
            </div>
            <div>
              <Label className="text-sm text-muted-foreground">Vehicle Info</Label>
              <p className="font-medium">{driver.vehicleInfo}</p>
            </div>
            <div>
              <Label className="text-sm text-muted-foreground">Registration Date</Label>
              <p className="font-medium">{new Date(driver.registrationDate).toLocaleDateString()}</p>
            </div>
            <div>
              <Label className="text-sm text-muted-foreground">Region</Label>
              <p className="font-medium">{driver.region || "N/A"}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-primary" /> Documents
            </CardTitle>
            <CardDescription>Placeholder for driver's uploaded documents.</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">No documents to display (Placeholder).</p>
            <Button variant="link" className="p-0 h-auto mt-2" asChild>
              <Link href={`/dashboard/drivers/${driver.id}/documents`}>View/Manage Documents</Link>
            </Button>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-primary" /> Performance
            </CardTitle>
            <CardDescription>Placeholder for driver's performance metrics.</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">No performance data to display (Placeholder).</p>
            <Button variant="link" className="p-0 h-auto mt-2" asChild>
              <Link href={`/dashboard/drivers/${driver.id}/performance`}>View Performance Details</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
