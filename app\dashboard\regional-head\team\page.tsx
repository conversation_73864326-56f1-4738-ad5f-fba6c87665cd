"use client"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import Link from "next/link"
import { useAuth } from "@/context/auth-context"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { <PERSON><PERSON>hecks, <PERSON><PERSON><PERSON>t, MoreHorizontal, PlusCircle, BarChart2, MessageSquare } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

// Mock data for operators in a region
const mockTeamOperators = [
  {
    id: "op1",
    name: "Operator One",
    email: "<EMAIL>",
    status: "Active",
    lastLogin: "2023-06-20T09:00:00Z",
    profilePictureUrl: "/placeholder.svg?width=40&height=40&text=O1",
  },
  {
    id: "op2",
    name: "Operator Two",
    email: "<EMAIL>",
    status: "Inactive",
    lastLogin: "2023-06-18T17:00:00Z",
    profilePictureUrl: "/placeholder.svg?width=40&height=40&text=O2",
  },
  {
    id: "op3",
    name: "Operator Three",
    email: "<EMAIL>",
    status: "Active",
    lastLogin: "2023-06-20T11:30:00Z",
    profilePictureUrl: "/placeholder.svg?width=40&height=40&text=O3",
  },
]

export default function TeamManagementPage() {
  const { user } = useAuth()
  const { toast } = useToast()

  if (user?.role !== "regional_head") {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <Alert variant="destructive" className="max-w-md">
          <ShieldAlert className="h-4 w-4" />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>You do not have permission to access Team Management.</AlertDescription>
        </Alert>
        <Button asChild variant="link" className="mt-4">
          <Link href="/dashboard">Go to Dashboard</Link>
        </Button>
      </div>
    )
  }

  const handleTeamAction = (action: string, operatorId: string) => {
    toast({
      title: "Team Action (Mock)",
      description: `${action} for operator ${operatorId} initiated. This is a mock action.`,
    })
  }

  return (
    <div className="space-y-6">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard">Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Team Management</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold flex items-center">
          <ListChecks className="mr-3 h-8 w-8 text-primary" />
          Team Management ({user?.region || "Your Region"})
        </h1>
        <Button
          onClick={() => handleTeamAction("Add New Operator", "")}
          className="bg-primary text-primary-foreground hover:bg-primary/90"
        >
          <PlusCircle className="mr-2 h-4 w-4" /> Add Operator
        </Button>
      </div>
      <CardDescription>
        Manage your team of Regional Operators. View their status and assign tasks or training.
      </CardDescription>

      <Card>
        <CardHeader>
          <CardTitle>Regional Operators</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead></TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Login</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockTeamOperators.map((operator) => (
                <TableRow key={operator.id}>
                  <TableCell>
                    <Avatar className="h-9 w-9">
                      <AvatarImage src={operator.profilePictureUrl || "/placeholder.svg"} alt={operator.name} />
                      <AvatarFallback>{operator.name.substring(0, 1)}</AvatarFallback>
                    </Avatar>
                  </TableCell>
                  <TableCell className="font-medium">{operator.name}</TableCell>
                  <TableCell>{operator.email}</TableCell>
                  <TableCell>
                    <Badge
                      variant={operator.status === "Active" ? "default" : "outline"}
                      className={
                        operator.status === "Active"
                          ? "bg-status-green text-status-green-foreground"
                          : "border-status-gray text-status-gray"
                      }
                    >
                      {operator.status}
                    </Badge>
                  </TableCell>
                  <TableCell>{new Date(operator.lastLogin).toLocaleString()}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => handleTeamAction("View Details", operator.id)}>
                          <Link href={`/dashboard/users/${operator.id}`} className="flex items-center w-full">
                            {" "}
                            {/* Assuming operator ID maps to a user ID */}
                            View Details
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleTeamAction("View Performance", operator.id)}>
                          <BarChart2 className="mr-2 h-4 w-4" /> View Performance
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleTeamAction("Send Message", operator.id)}>
                          <MessageSquare className="mr-2 h-4 w-4" /> Send Message
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
