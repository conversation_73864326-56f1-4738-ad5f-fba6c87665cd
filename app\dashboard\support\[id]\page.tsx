"use client"

import type React from "react"

import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { usePara<PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { ArrowLeft, MessageSquare, UserCircle } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useAuth } from "@/context/auth-context"
import { cn } from "@/lib/utils"

// Mock data - in a real app, this would be fetched
const mockTicketsData = [
  {
    id: "TKT001",
    subject: "Payment Failed",
    user: "<PERSON><PERSON><PERSON> <PERSON>",
    userId: "user1",
    status: "Open",
    lastUpdate: "2023-06-15T10:00:00Z",
    region: "Kathmandu",
    description: "My payment for ride RIDE0X1 failed but amount was deducted.",
    history: [
      {
        user: "Aarav Sharma",
        message: "My payment for ride RIDE0X1 failed but amount was deducted.",
        timestamp: "2023-06-15T10:00:00Z",
        type: "user",
      },
      {
        user: "Support Bot",
        message: "Ticket created. An agent will assist you shortly.",
        timestamp: "2023-06-15T10:01:00Z",
        type: "system",
      },
    ],
  },
  {
    id: "TKT002",
    subject: "Driver Rude Behavior",
    user: "Bina Thapa",
    userId: "user2",
    status: "In Progress",
    lastUpdate: "2023-06-14T14:30:00Z",
    region: "Pokhara",
    description: "The driver for ride RIDE0X2 was very rude and unprofessional.",
    history: [
      {
        user: "Bina Thapa",
        message: "The driver for ride RIDE0X2 was very rude and unprofessional.",
        timestamp: "2023-06-14T14:30:00Z",
        type: "user",
      },
      {
        user: "Support Agent (Ram)",
        message: "We are sorry for your experience. Investigating this with high priority.",
        timestamp: "2023-06-14T16:00:00Z",
        type: "agent",
      },
    ],
  },
]

const getTicketStatusBadgeClass = (status: string | undefined) => {
  if (status === "Open") return "bg-red-500 text-white"
  else if (status === "In Progress") return "bg-yellow-500 text-black"
  else if (status === "Resolved") return "bg-green-500 text-white"
  return "bg-gray-500 text-white"
}

export default function SupportTicketDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const { user: authUser } = useAuth()
  const ticketId = params.id as string

  const ticket = mockTicketsData.find((t) => t.id === ticketId)

  if (!ticket) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <p className="text-xl text-muted-foreground">Support Ticket not found.</p>
        <Button onClick={() => router.back()} variant="outline" className="mt-4">
          <ArrowLeft className="mr-2 h-4 w-4" /> Go Back
        </Button>
      </div>
    )
  }

  const handleAddReply = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    const formData = new FormData(event.currentTarget)
    const reply = formData.get("reply") as string
    if (reply.trim()) {
      toast({
        title: "Reply Added (Mock)",
        description: "Your reply has been added to the ticket. (This is a mock action)",
      })
      // In a real app, update ticket history and re-fetch or optimistically update
      event.currentTarget.reset()
    }
  }

  return (
    <div className="space-y-6">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard">Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard/support">Support Tickets</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Ticket {ticket.id}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold">Support Ticket Details</h1>
        <Button onClick={() => router.back()} variant="outline">
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Tickets
        </Button>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-2xl">{ticket.subject}</CardTitle>
              <CardDescription>
                Ticket ID: {ticket.id} • User: {ticket.user}
              </CardDescription>
            </div>
            <Badge className={cn("capitalize", getTicketStatusBadgeClass(ticket.status))}>{ticket.status}</Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-sm text-muted-foreground">Initial Description</Label>
            <p className="p-3 bg-muted/50 rounded-md border">{ticket.description}</p>
          </div>
          <div>
            <Label className="text-sm text-muted-foreground">Region</Label>
            <p className="font-medium">{ticket.region || "N/A"}</p>
          </div>
          <div>
            <Label className="text-sm text-muted-foreground">Last Updated</Label>
            <p className="font-medium">{new Date(ticket.lastUpdate).toLocaleString()}</p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" /> Ticket History & Replies
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {ticket.history.map((entry, index) => (
            <div
              key={index}
              className={`flex gap-3 ${entry.type === "agent" || entry.type === "system" ? "justify-start" : "justify-end"}`}
            >
              {entry.type === "agent" && <UserCircle className="h-8 w-8 text-primary mt-1" />}
              <div
                className={`p-3 rounded-lg max-w-[70%] ${entry.type === "agent" || entry.type === "system" ? "bg-secondary text-secondary-foreground" : "bg-primary text-primary-foreground"}`}
              >
                <p className="text-sm">{entry.message}</p>
                <p
                  className={`text-xs mt-1 ${entry.type === "agent" || entry.type === "system" ? "text-muted-foreground" : "text-primary-foreground/80"}`}
                >
                  {entry.user} • {new Date(entry.timestamp).toLocaleTimeString()}
                </p>
              </div>
              {entry.type === "user" && <UserCircle className="h-8 w-8 text-muted-foreground mt-1" />}
            </div>
          ))}
        </CardContent>
        {ticket.status !== "Resolved" && (
          <CardFooter>
            <form onSubmit={handleAddReply} className="w-full space-y-2">
              <Label htmlFor="replyMessage">Add Reply</Label>
              <Textarea id="replyMessage" name="reply" placeholder="Type your reply..." rows={4} required />
              <Button type="submit">Send Reply</Button>
            </form>
          </CardFooter>
        )}
      </Card>
    </div>
  )
}
