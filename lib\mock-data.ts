import type { User, Driver, Ride, Payment } from "@/types"

export const mockUsers: User[] = [
  {
    id: "user1",
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "9800000001",
    registrationDate: "2023-01-15",
    status: "active",
    profilePictureUrl: "/placeholder-user.jpg",
    region: "Kathmandu",
  },
  {
    id: "user2",
    name: "<PERSON><PERSON> Thapa",
    email: "<EMAIL>",
    phone: "9800000002",
    registrationDate: "2023-02-20",
    status: "suspended",
    profilePictureUrl: "/placeholder-user.jpg",
    region: "Pokhara",
  },
  {
    id: "user3",
    name: "<PERSON><PERSON>da<PERSON>",
    email: "<EMAIL>",
    phone: "9800000003",
    registrationDate: "2023-03-10",
    status: "pending_verification",
    region: "Kathmandu",
  },
  {
    id: "user4",
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "**********",
    registrationDate: "2023-04-05",
    status: "active",
    region: "Biratnagar",
  },
  {
    id: "user5",
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "**********",
    registrationDate: "2023-05-12",
    status: "active",
    region: "Pokhara",
  },
]

export const mockDrivers: Driver[] = [
  {
    id: "driver1",
    name: "Rajesh Hamal",
    email: "<EMAIL>",
    phone: "**********",
    licenseNumber: "LIC-001",
    vehicleInfo: "Bike - BA 2 PA 1234",
    registrationDate: "2023-01-10",
    status: "approved",
    rating: 4.8,
    profilePictureUrl: "/placeholder-user.jpg",
    region: "Kathmandu",
  },
  {
    id: "driver2",
    name: "Sita Devi",
    email: "<EMAIL>",
    phone: "**********",
    licenseNumber: "LIC-002",
    vehicleInfo: "Car - BA 4 CHA 5678",
    registrationDate: "2023-02-15",
    status: "pending",
    rating: 0,
    region: "Pokhara",
  },
  {
    id: "driver3",
    name: "Gopal Prasad",
    email: "<EMAIL>",
    phone: "**********",
    licenseNumber: "LIC-003",
    vehicleInfo: "Auto - PA 2 HA 9012",
    registrationDate: "2023-03-05",
    status: "rejected",
    rating: 0,
    profilePictureUrl: "/placeholder-user.jpg",
    region: "Biratnagar",
  },
  {
    id: "driver4",
    name: "Manju Gurung",
    email: "<EMAIL>",
    phone: "**********",
    licenseNumber: "LIC-004",
    vehicleInfo: "Bike - GA 1 PA 3456",
    registrationDate: "2023-04-01",
    status: "active_online",
    rating: 4.5,
    region: "Kathmandu",
  },
  {
    id: "driver5",
    name: "Hari Bahadur",
    email: "<EMAIL>",
    phone: "**********",
    licenseNumber: "LIC-005",
    vehicleInfo: "Car - BA 5 CHA 7890",
    registrationDate: "2023-05-20",
    status: "active_offline",
    rating: 4.2,
    region: "Pokhara",
  },
]

export const mockRides: Ride[] = [
  {
    id: "ride1",
    userId: "user1",
    userName: "Aarav Sharma",
    driverId: "driver1",
    driverName: "Rajesh Hamal",
    pickupLocation: "New Baneshwor",
    dropoffLocation: "Thamel",
    fare: 250,
    status: "completed",
    requestTime: "2023-06-01T10:00:00Z",
    completionTime: "2023-06-01T10:25:00Z",
    rideType: "bike",
    region: "Kathmandu",
  },
  {
    id: "ride2",
    userId: "user2",
    userName: "Bina Thapa",
    driverId: "driver4",
    driverName: "Manju Gurung",
    pickupLocation: "Patan Durbar Square",
    dropoffLocation: "Boudhanath Stupa",
    fare: 400,
    status: "in_progress",
    requestTime: "2023-06-01T11:00:00Z",
    rideType: "car",
    region: "Pokhara",
  },
  {
    id: "ride3",
    userId: "user3",
    userName: "Chandan Yadav",
    pickupLocation: "Kalanki",
    dropoffLocation: "Ratna Park",
    fare: 150,
    status: "requested",
    requestTime: "2023-06-01T12:00:00Z",
    rideType: "bike",
    region: "Kathmandu",
  },
  {
    id: "ride4",
    userId: "user1",
    userName: "Aarav Sharma",
    driverId: "driver1",
    driverName: "Rajesh Hamal",
    pickupLocation: "Airport",
    dropoffLocation: "Lazimpat",
    fare: 300,
    status: "cancelled_user",
    requestTime: "2023-06-02T09:00:00Z",
    rideType: "auto",
    region: "Biratnagar",
  },
  {
    id: "ride5",
    userId: "user4",
    userName: "Deepika Karki",
    driverId: "driver5",
    driverName: "Hari Bahadur",
    pickupLocation: "Jawalakhel",
    dropoffLocation: "Koteshwor",
    fare: 350,
    status: "completed",
    requestTime: "2023-06-02T14:00:00Z",
    completionTime: "2023-06-02T14:35:00Z",
    rideType: "car",
    region: "Pokhara",
  },
]

export const mockPayments: Payment[] = [
  {
    id: "payment1",
    rideId: "ride1",
    userId: "user1",
    userName: "Aarav Sharma",
    driverId: "driver1",
    driverName: "Rajesh Hamal",
    amount: 250,
    method: "eSewa",
    status: "completed",
    transactionId: "ESW001",
    paymentDate: "2023-06-01T10:25:00Z",
    region: "Kathmandu",
  },
  {
    id: "payment2",
    rideId: "ride2",
    userId: "user2",
    userName: "Bina Thapa",
    driverId: "driver4",
    driverName: "Manju Gurung",
    amount: 400,
    method: "Cash",
    status: "pending",
    transactionId: "CASH001",
    paymentDate: "2023-06-01T11:00:00Z",
    region: "Pokhara",
  },
  {
    id: "payment3",
    rideId: "ride5",
    userId: "user4",
    userName: "Deepika Karki",
    driverId: "driver5",
    driverName: "Hari Bahadur",
    amount: 350,
    method: "Khalti",
    status: "completed",
    transactionId: "KLT001",
    paymentDate: "2023-06-02T14:35:00Z",
    region: "Pokhara",
  },
  {
    id: "payment4",
    rideId: "ride4",
    userId: "user1",
    userName: "Aarav Sharma",
    amount: 0,
    method: "Wallet",
    status: "refunded",
    transactionId: "REF001",
    paymentDate: "2023-06-02T09:05:00Z",
    region: "Biratnagar",
  },
]
